
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { RoomAvailability } from "@/types/room";

const AvailabilitySearch = () => {
  const [date, setDate] = useState<string>("");
  const [timeStart, setTimeStart] = useState<string>("");
  const [timeEnd, setTimeEnd] = useState<string>("");
  const [capacity, setCapacity] = useState<string>("");
  const [type, setType] = useState<string>("");
  const [results, setResults] = useState<RoomAvailability[]>([]);

  const handleSearch = () => {
    // Placeholder for API call
    console.log("Searching with:", { date, timeStart, timeEnd, capacity, type });
    
    // Mock results
    setResults([
      {
        id: "1",
        name: "Room 101",
        available: true,
      },
      {
        id: "2",
        name: "Room 102",
        available: false,
        conflicts: ["English 101 (9:00-10:30)"],
      },
      {
        id: "3",
        name: "Main Hall",
        available: true,
      },
    ]);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Find Available Rooms</CardTitle>
        <CardDescription>
          Search for available rooms based on date, time, and requirements
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <Input
              id="date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="timeStart">Start Time</Label>
            <Input
              id="timeStart"
              type="time"
              value={timeStart}
              onChange={(e) => setTimeStart(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="timeEnd">End Time</Label>
            <Input
              id="timeEnd"
              type="time"
              value={timeEnd}
              onChange={(e) => setTimeEnd(e.target.value)}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="space-y-2">
            <Label htmlFor="capacity">Minimum Capacity</Label>
            <Input
              id="capacity"
              type="number"
              min="1"
              placeholder="Enter minimum capacity"
              value={capacity}
              onChange={(e) => setCapacity(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="roomType">Room Type</Label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger id="roomType">
                <SelectValue placeholder="Any room type" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="any">Any room type</SelectItem>
                  <SelectItem value="classroom">Classroom</SelectItem>
                  <SelectItem value="lecture">Lecture Hall</SelectItem>
                  <SelectItem value="lab">Laboratory</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <Button onClick={handleSearch} className="w-full">Search Available Rooms</Button>
        
        {results.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-3">Results</h3>
            <div className="space-y-3">
              {results.map((room) => (
                <div
                  key={room.id}
                  className={`p-3 border rounded-md ${
                    room.available ? "border-green-500 bg-green-50" : "border-red-500 bg-red-50"
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">{room.name}</span>
                      {!room.available && room.conflicts && (
                        <p className="text-sm text-red-600 mt-1">
                          Conflicts: {room.conflicts.join(", ")}
                        </p>
                      )}
                    </div>
                    <div>
                      {room.available ? (
                        <span className="inline-block px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                          Available
                        </span>
                      ) : (
                        <span className="inline-block px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                          Unavailable
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AvailabilitySearch;
