import { Class, ClassesApiResponse, ClassFilter, ClassApiResponse } from "@/types/class";

/**
 * Fetch classes with filtering options
 */
export const fetchClasses = async (filters: Partial<ClassFilter> = {}): Promise<ClassesApiResponse> => {
  try {
    // Build query parameters
    const params = new URLSearchParams();
    if (filters.search) params.append('search', filters.search);
    if (filters.status) params.append('status', filters.status);
    if (filters.level) params.append('level', filters.level);
    if (filters.teacherId) params.append('teacherId', filters.teacherId);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await fetch(`http://localhost:3000/api/classes?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data || [],
      pagination: data.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching classes:', error);
    return {
      success: false,
      data: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  }
};
  {
    id: "class2",
    name: "Advanced Physics",
    level: "Advanced",
    teachers: [
      {
        id: "teacher2",
        name: "Jane Doe",
        schedule: []
      }
    ],
    room: "Lab B202",
    capacity: {
      total: 25,
      current: 25,
      available: 0
    },
    schedule: {
      startDate: "2023-09-01",
      endDate: "2024-01-30"
    },
    status: "active"
  },
  {
    id: "class3",
    name: "Literature Studies",
    level: "Intermediate",
    teachers: [
      {
        id: "teacher3",
        name: "Robert Johnson",
        schedule: []
      }
    ],
    room: "Room C103",
    capacity: {
      total: 35,
      current: 18,
      available: 17
    },
    schedule: {
      startDate: "2023-09-15",
      endDate: "2023-12-20"
    },
    status: "inactive"
  },
  {
    id: "class4",
    name: "Computer Science",
    level: "Advanced",
    teachers: [
      {
        id: "teacher4",
        name: "Alice Brown",
        schedule: []
      }
    ],
    room: "Lab A105",
    capacity: {
      total: 20,
      current: 20,
      available: 0
    },
    schedule: {
      startDate: "2023-08-15",
      endDate: "2023-12-01"
    },
    status: "active"
  },
  {
    id: "class5",
    name: "History 101",
    level: "Beginner",
    teachers: [
      {
        id: "teacher5",
        name: "Michael Lee",
        schedule: []
      }
    ],
    room: "Room D104",
    capacity: {
      total: 40,
      current: 12,
      available: 28
    },
    schedule: {
      startDate: "2023-09-01",
      endDate: "2023-12-15"
    },
    status: "merged"
  },
  {
    id: "class6",
    name: "Chemistry Lab",
    level: "Intermediate",
    teachers: [
      {
        id: "teacher6",
        name: "Sarah White",
        schedule: []
      }
    ],
    room: "Lab C205",
    capacity: {
      total: 25,
      current: 20,
      available: 5
    },
    schedule: {
      startDate: "2023-09-01",
      endDate: "2023-12-15"
    },
    status: "active"
  }
];

// Function to fetch classes with filtering, sorting, and pagination
export const fetchClasses = async (filters: ClassFilter): Promise<ClassesApiResponse> => {
  try {
    console.log("Fetching classes with filters:", filters);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Filter mock data based on the provided filters
    let filteredClasses = [...mockClasses];
    
    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredClasses = filteredClasses.filter(c => 
        c.name.toLowerCase().includes(searchLower) || 
        c.level.toLowerCase().includes(searchLower) ||
        c.room.toLowerCase().includes(searchLower) ||
        c.teachers.some(t => t.name.toLowerCase().includes(searchLower))
      );
    }
    
    // Apply status filter
    if (filters.status) {
      filteredClasses = filteredClasses.filter(c => c.status === filters.status);
    }
    
    // Apply level filter
    if (filters.level) {
      filteredClasses = filteredClasses.filter(c => c.level === filters.level);
    }
    
    // Apply room filter
    if (filters.room) {
      filteredClasses = filteredClasses.filter(c => 
        c.room.toLowerCase().includes(filters.room!.toLowerCase())
      );
    }
    
    // Apply sorting
    if (filters.sortBy) {
      filteredClasses.sort((a, b) => {
        let valA: any;
        let valB: any;
        
        switch (filters.sortBy) {
          case 'name':
            valA = a.name;
            valB = b.name;
            break;
          case 'level':
            valA = a.level;
            valB = b.level;
            break;
          case 'startDate':
            valA = new Date(a.schedule.startDate);
            valB = new Date(b.schedule.startDate);
            break;
          case 'endDate':
            valA = new Date(a.schedule.endDate);
            valB = new Date(b.schedule.endDate);
            break;
          case 'currentStudentCount':
            valA = a.capacity.current;
            valB = b.capacity.current;
            break;
          case 'status':
            valA = a.status;
            valB = b.status;
            break;
          default:
            valA = a.name;
            valB = b.name;
        }
        
        if (valA < valB) return filters.sortOrder === 'desc' ? 1 : -1;
        if (valA > valB) return filters.sortOrder === 'desc' ? -1 : 1;
        return 0;
      });
    }
    
    // Calculate pagination
    const total = filteredClasses.length;
    const pages = Math.ceil(total / filters.limit);
    const startIndex = (filters.page - 1) * filters.limit;
    const paginatedClasses = filteredClasses.slice(startIndex, startIndex + filters.limit);
    
    return {
      success: true,
      data: paginatedClasses,
      pagination: {
        total,
        page: filters.page,
        limit: filters.limit,
        pages
      }
    };
  } catch (error) {
    console.error("Error fetching classes:", error);
    throw error;
  }
};

// Function to fetch a single class by ID
export const fetchClassById = async (classId: string): Promise<ClassApiResponse> => {
  try {
    console.log("Fetching class with ID:", classId);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Find the class in our mock data
    const classItem = mockClasses.find(c => c.id === classId);
    
    if (!classItem) {
      throw new Error(`Class with ID ${classId} not found`);
    }
    
    // Enhance the class with additional details for the detailed view
    const enhancedClass: Class = {
      ...classItem,
      createdAt: "2023-01-01T12:00:00Z",
      updatedAt: "2023-03-15T09:30:00Z",
      schedule: {
        ...classItem.schedule,
        days: [
          {
            day: "monday",
            times: [
              {
                start: "09:00",
                end: "11:00",
                teacher: classItem.teachers[0]?.name || "Unknown Teacher"
              }
            ]
          },
          {
            day: "wednesday",
            times: [
              {
                start: "13:00",
                end: "15:00",
                teacher: classItem.teachers[0]?.name || "Unknown Teacher"
              }
            ]
          }
        ]
      },
      studentHistory: [
        {
          studentId: "student1",
          studentName: "Alice Johnson",
          joinDate: "2023-01-15",
          leaveDate: "2023-06-30",
          reason: "Completed course"
        },
        {
          studentId: "student2",
          studentName: "Bob Smith",
          joinDate: "2023-01-15",
          leaveDate: "",
          reason: ""
        },
        {
          studentId: "student3",
          studentName: "Charlie Brown",
          joinDate: "2023-01-15",
          leaveDate: "2023-03-10",
          reason: "Transferred to another class"
        }
      ],
      makeupClasses: [
        {
          originalDate: "2023-02-10",
          makeupDate: "2023-02-17",
          reason: "Teacher absence",
          status: "completed"
        },
        {
          originalDate: "2023-03-15",
          makeupDate: "2023-03-22",
          reason: "Public holiday",
          status: "scheduled"
        },
        {
          originalDate: "2023-04-05",
          makeupDate: "2023-04-12",
          reason: "Facility maintenance",
          status: "cancelled"
        }
      ]
    };
    
    // For each teacher, add a schedule if it doesn't exist
    enhancedClass.teachers = enhancedClass.teachers.map(teacher => ({
      ...teacher,
      schedule: teacher.schedule?.length 
        ? teacher.schedule 
        : [
            {
              day: "monday",
              timeStart: "09:00",
              timeEnd: "11:00"
            },
            {
              day: "wednesday",
              timeStart: "13:00",
              timeEnd: "15:00"
            }
          ]
    }));
    
    return {
      success: true,
      data: enhancedClass
    };
  } catch (error) {
    console.error("Error fetching class:", error);
    throw error;
  }
};

// Function to get available levels for filtering
export const getAvailableLevels = async (): Promise<string[]> => {
  try {
    // In a real application, this could be a separate API call
    // Here we'll extract unique levels from our mock data
    const levels = Array.from(new Set(mockClasses.map(c => c.level)));
    return levels;
  } catch (error) {
    console.error("Error fetching levels:", error);
    throw error;
  }
};

// Function to get available rooms for filtering
export const getAvailableRooms = async (): Promise<string[]> => {
  try {
    // In a real application, this could be a separate API call
    // Here we'll extract unique rooms from our mock data
    const rooms = Array.from(new Set(mockClasses.map(c => c.room)));
    return rooms;
  } catch (error) {
    console.error("Error fetching rooms:", error);
    throw error;
  }
};

// Function to create a new class
export const createClass = async (classData: Partial<Class>): Promise<ClassApiResponse> => {
  try {
    console.log("Creating class with data:", classData);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Simulate validation checks
    if (!classData.name) throw new Error("Class name is required");
    if (!classData.level) throw new Error("Class level is required");
    if (!classData.room) throw new Error("Classroom is required");
    if (!classData.teachers || classData.teachers.length === 0) {
      throw new Error("At least one teacher is required");
    }
    
    // Create a new class with the provided data
    const newClass: Class = {
      id: `class${mockClasses.length + 1}`,
      name: classData.name || "",
      level: classData.level || "",
      teachers: classData.teachers || [],
      room: classData.room || "",
      capacity: classData.capacity || { total: 0, current: 0, available: 0 },
      schedule: classData.schedule || { 
        startDate: "", 
        endDate: "", 
        days: []
      },
      status: "active",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Add the new class to our mock data (this would be a database insert in a real app)
    mockClasses.push(newClass);
    
    return {
      success: true,
      data: newClass
    };
  } catch (error) {
    console.error("Error creating class:", error);
    throw error;
  }
};

// Function to update an existing class
export const updateClass = async (classId: string, classData: Partial<Class>): Promise<ClassApiResponse> => {
  try {
    console.log("Updating class with ID:", classId, "and data:", classData);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Find the class in our mock data
    const classIndex = mockClasses.findIndex(c => c.id === classId);
    
    if (classIndex === -1) {
      throw new Error(`Class with ID ${classId} not found`);
    }
    
    // Validate the update
    if (classData.capacity && classData.capacity.current > classData.capacity.total) {
      throw new Error("Capacity cannot be less than current student count");
    }
    
    // Update the class with the provided data
    const updatedClass: Class = {
      ...mockClasses[classIndex],
      ...classData,
      // For nested objects, we need to merge them manually
      capacity: classData.capacity ? {
        ...mockClasses[classIndex].capacity,
        ...classData.capacity,
        // Recalculate available capacity
        available: classData.capacity.total 
          ? classData.capacity.total - (classData.capacity.current || mockClasses[classIndex].capacity.current)
          : mockClasses[classIndex].capacity.available
      } : mockClasses[classIndex].capacity,
      schedule: classData.schedule ? {
        ...mockClasses[classIndex].schedule,
        ...classData.schedule,
      } : mockClasses[classIndex].schedule,
      teachers: classData.teachers || mockClasses[classIndex].teachers,
      updatedAt: new Date().toISOString(),
    };
    
    // Update the class in our mock data
    mockClasses[classIndex] = updatedClass;
    
    return {
      success: true,
      data: updatedClass
    };
  } catch (error) {
    console.error("Error updating class:", error);
    throw error;
  }
};
