import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Room, RoomScheduleItem, RoomUtilization } from "@/types/room";
import { fetchRoom, fetchRoomSchedule, fetchRoomUtilization } from "@/services/roomService";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  Building, 
  Calendar, 
  Edit, 
  MapPin, 
  Wrench, 
  Users 
} from "lucide-react";
import { format, parseISO, addWeeks, startOfWeek, endOfWeek, isSameDay } from "date-fns";
import { hasRole } from "@/lib/auth";
import { capitalizeFirstLetter } from "@/lib/utils";
import MaintenanceScheduleForm from "@/components/rooms/MaintenanceScheduleForm";
import RoomScheduleCalendar from "@/components/rooms/RoomScheduleCalendar";
import RoomUtilizationChart from "@/components/rooms/RoomUtilizationChart";

interface RoomDetailViewProps {
  roomId: string;
}

const RoomDetailView: React.FC<RoomDetailViewProps> = ({ roomId }) => {
  const navigate = useNavigate();
  
  const [room, setRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [scheduleItems, setScheduleItems] = useState<RoomScheduleItem[]>([]);
  const [scheduleLoading, setScheduleLoading] = useState(false);
  
  const [utilization, setUtilization] = useState<RoomUtilization | null>(null);
  const [utilizationLoading, setUtilizationLoading] = useState(false);
  
  const [currentWeekStart, setCurrentWeekStart] = useState(() => startOfWeek(new Date(), { weekStartsOn: 1 }));
  const currentWeekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
  
  const [activeTab, setActiveTab] = useState("overview");
  
  const canManageRooms = hasRole(['SuperAdmin', 'Manager']);
  
  useEffect(() => {
    const loadRoom = async () => {
      setLoading(true);
      try {
        const data = await fetchRoom(roomId);
        setRoom(data);
        setError(null);
      } catch (err) {
        console.error(`Error loading room ${roomId}:`, err);
        setError("Failed to load room details");
      } finally {
        setLoading(false);
      }
    };
    
    loadRoom();
  }, [roomId]);
  
  useEffect(() => {
    const loadSchedule = async () => {
      if (!room) return;
      
      setScheduleLoading(true);
      try {
        const startDate = format(currentWeekStart, 'yyyy-MM-dd');
        const endDate = format(currentWeekEnd, 'yyyy-MM-dd');
        
        const data = await fetchRoomSchedule(roomId, startDate, endDate);
        setScheduleItems(data);
      } catch (err) {
        console.error(`Error loading schedule for room ${roomId}:`, err);
      } finally {
        setScheduleLoading(false);
      }
    };
    
    loadSchedule();
  }, [roomId, room, currentWeekStart]);
  
  useEffect(() => {
    const loadUtilization = async () => {
      if (!room) return;
      
      setUtilizationLoading(true);
      try {
        const today = new Date();
        const startDate = format(new Date(today.getFullYear(), today.getMonth(), 1), 'yyyy-MM-dd');
        const endDate = format(new Date(today.getFullYear(), today.getMonth() + 1, 0), 'yyyy-MM-dd');
        
        const data = await fetchRoomUtilization(roomId, startDate, endDate);
        setUtilization(data);
      } catch (err) {
        console.error(`Error loading utilization for room ${roomId}:`, err);
      } finally {
        setUtilizationLoading(false);
      }
    };
    
    loadUtilization();
  }, [roomId, room]);
  
  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentWeekStart(prev => 
      direction === 'next' 
        ? addWeeks(prev, 1) 
        : addWeeks(prev, -1)
    );
  };
  
  const getRoomStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string; color: string }> = {
      active: { label: 'Active', color: 'bg-green-500' },
      maintenance: { label: 'Under Maintenance', color: 'bg-amber-500' },
      inactive: { label: 'Inactive', color: 'bg-gray-500' }
    };
    
    const statusInfo = statusMap[status] || { label: status, color: 'bg-gray-500' };
    
    return (
      <Badge className={`${statusInfo.color} capitalize px-2 py-1`}>
        {statusInfo.label}
      </Badge>
    );
  };
  
  const handleEditRoom = () => {
    navigate(`/rooms/${roomId}/edit`);
  };
  
  const handleGoBack = () => {
    navigate('/rooms');
  };
  
  const renderMaintenanceSection = () => {
    return (
      <TabsContent value="maintenance">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {canManageRooms && (
            <Card>
              <CardHeader>
                <CardTitle>Schedule Maintenance</CardTitle>
                <CardDescription>
                  Schedule a maintenance period for this room
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MaintenanceScheduleForm roomId={roomId} onScheduled={() => setActiveTab("schedule")} />
              </CardContent>
            </Card>
          )}
          
          <Card>
            <CardHeader>
              <CardTitle>Maintenance History</CardTitle>
              <CardDescription>
                Past and upcoming maintenance periods
              </CardDescription>
            </CardHeader>
            <CardContent>
              {room.maintenanceSchedule && room.maintenanceSchedule.length > 0 ? (
                <div className="space-y-4">
                  {room.maintenanceSchedule.map(maintenance => (
                    <div 
                      key={maintenance.id} 
                      className="p-4 border rounded-md"
                    >
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium flex items-center">
                          <Wrench className="h-4 w-4 mr-2 text-amber-500" />
                          Maintenance
                        </h4>
                        <Badge variant="outline">
                          {new Date(maintenance.endDate) > new Date() ? "Upcoming" : "Completed"}
                        </Badge>
                      </div>
                      
                      <div className="mt-2 text-sm">
                        <div className="flex items-center">
                          <Calendar className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                          <div>
                            <div>
                              Start: {format(parseISO(maintenance.startDate), 'EEEE, MMMM d, yyyy')}
                            </div>
                            <div>
                              End: {format(parseISO(maintenance.endDate), 'EEEE, MMMM d, yyyy')}
                            </div>
                          </div>
                        </div>
                        
                        <div className="mt-2">
                          <div className="font-medium">Reason:</div>
                          <p className="text-muted-foreground">{maintenance.reason}</p>
                        </div>
                        
                        <div className="mt-2 text-xs text-muted-foreground">
                          Scheduled by {maintenance.scheduledBy.name} on {format(parseISO(maintenance.createdAt), 'PPp')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center">
                  <p className="text-muted-foreground">No maintenance history available for this room.</p>
                  {canManageRooms && (
                    <p className="mt-2 text-sm">
                      Use the form to schedule maintenance.
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    );
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Loading room details...</p>
      </div>
    );
  }
  
  if (error || !room) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <h2 className="text-xl font-bold mb-2">Error</h2>
        <p className="text-muted-foreground mb-4">{error || "Room not found"}</p>
        <Button onClick={handleGoBack}>Back to Room Directory</Button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleGoBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{room.name}</h1>
            <p className="text-muted-foreground">
              {room.building}, {room.floor === 0 ? 'Ground Floor' : `Floor ${room.floor}`}
            </p>
          </div>
        </div>
        
        <div className="flex gap-2 items-center">
          {getRoomStatusBadge(room.status)}
          
          {canManageRooms && (
            <Button onClick={handleEditRoom}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Room
            </Button>
          )}
        </div>
      </div>
      
      <Tabs 
        value={activeTab} 
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="utilization">Utilization</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Room Information</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-3">
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground mb-1">Room Name</dt>
                    <dd className="text-sm font-semibold">{room.name}</dd>
                  </div>
                  
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground mb-1">Status</dt>
                    <dd>{getRoomStatusBadge(room.status)}</dd>
                  </div>
                  
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground mb-1">
                      <div className="flex items-center">
                        <Building className="h-4 w-4 mr-1" />
                        Building
                      </div>
                    </dt>
                    <dd className="text-sm">{room.building}</dd>
                  </div>
                  
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground mb-1">
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        Floor
                      </div>
                    </dt>
                    <dd className="text-sm">
                      {room.floor === 0 ? 'Ground Floor' : `Floor ${room.floor}`}
                    </dd>
                  </div>
                  
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground mb-1">
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        Capacity
                      </div>
                    </dt>
                    <dd className="text-sm">{room.capacity} students</dd>
                  </div>
                  
                  {room.lastModified && (
                    <div>
                      <dt className="text-sm font-medium text-muted-foreground mb-1">Last Modified</dt>
                      <dd className="text-sm">
                        {format(parseISO(room.lastModified.timestamp), 'PPp')}
                        <div className="text-xs text-muted-foreground">
                          by {room.lastModified.userName}
                        </div>
                      </dd>
                    </div>
                  )}
                </dl>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Features & Equipment</CardTitle>
                <CardDescription>
                  Available facilities in this room
                </CardDescription>
              </CardHeader>
              <CardContent>
                {room.features.length === 0 ? (
                  <p className="text-muted-foreground">No special features or equipment recorded.</p>
                ) : (
                  <div className="grid grid-cols-2 gap-2">
                    {room.features.map(feature => (
                      <div key={feature} className="flex items-center p-2 rounded-md border">
                        <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                        <span>{capitalizeFirstLetter(feature)}</span>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Upcoming Schedule</CardTitle>
                <CardDescription>
                  Next classes scheduled in this room
                </CardDescription>
              </CardHeader>
              <CardContent>
                {scheduleLoading ? (
                  <p>Loading schedule...</p>
                ) : scheduleItems.length === 0 ? (
                  <p className="text-muted-foreground">No scheduled classes for the current period.</p>
                ) : (
                  <div className="space-y-3">
                    {scheduleItems
                      .filter(item => new Date(item.start) >= new Date())
                      .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime())
                      .slice(0, 5)
                      .map(item => (
                        <div 
                          key={item.id} 
                          className="flex flex-col sm:flex-row sm:items-center justify-between p-3 rounded-md border"
                          style={{
                            borderLeft: `4px solid ${item.backgroundColor || (item.type === 'maintenance' ? '#F44336' : '#2196F3')}`
                          }}
                        >
                          <div>
                            <h4 className="font-medium">{item.title}</h4>
                            {item.teacherId && (
                              <p className="text-sm text-muted-foreground">
                                Teacher: {item.teacherName}
                              </p>
                            )}
                          </div>
                          
                          <div className="mt-2 sm:mt-0 text-sm">
                            <div className="flex items-center">
                              <Calendar className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                              {format(parseISO(item.start), 'EEEE, MMMM d, yyyy')}
                            </div>
                            <div className="flex items-center mt-1">
                              <Clock className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                              {format(parseISO(item.start), 'h:mm a')} - {format(parseISO(item.end), 'h:mm a')}
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="schedule">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <CardTitle>Room Schedule</CardTitle>
                  <CardDescription>
                    Weekly schedule for {room.name}
                  </CardDescription>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => navigateWeek('prev')}
                  >
                    Previous Week
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setCurrentWeekStart(startOfWeek(new Date(), { weekStartsOn: 1 }))}
                  >
                    Current Week
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => navigateWeek('next')}
                  >
                    Next Week
                  </Button>
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground mt-1">
                {format(currentWeekStart, 'MMMM d, yyyy')} - {format(currentWeekEnd, 'MMMM d, yyyy')}
              </div>
            </CardHeader>
            
            <CardContent>
              {scheduleLoading ? (
                <div className="py-8 text-center">Loading schedule...</div>
              ) : (
                <RoomScheduleCalendar 
                  scheduleItems={scheduleItems} 
                  weekStart={currentWeekStart} 
                  roomName={room.name}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {renderMaintenanceSection()}
        
        <TabsContent value="utilization">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Room Utilization</CardTitle>
                <CardDescription>
                  Usage statistics for the current month
                </CardDescription>
              </CardHeader>
              <CardContent>
                {utilizationLoading ? (
                  <div className="py-8 text-center">Loading utilization data...</div>
                ) : utilization ? (
                  <RoomUtilizationChart utilization={utilization} />
                ) : (
                  <div className="py-8 text-center">
                    <p className="text-muted-foreground">No utilization data available for this room.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

const Clock = ({ className }: { className?: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <circle cx="12" cy="12" r="10"/>
    <polyline points="12 6 12 12 16 14"/>
  </svg>
);

export default RoomDetailView;
