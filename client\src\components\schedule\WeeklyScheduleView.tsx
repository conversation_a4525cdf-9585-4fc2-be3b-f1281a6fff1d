
import React, { useState, useEffect } from "react";
import { format, addDays, startOfWeek } from "date-fns";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { UserRole } from "@/types";

interface WeeklyScheduleViewProps {
  startDate: Date;
  userRole: UserRole | null;
}

interface ScheduleItem {
  id: string;
  className: string;
  timeStart: string;
  timeEnd: string;
  room: string;
  teacher: string;
  day: number; // 0-6 for Sunday-Saturday
}

const WeeklyScheduleView: React.FC<WeeklyScheduleViewProps> = ({ startDate, userRole }) => {
  const [weeklySchedule, setWeeklySchedule] = useState<ScheduleItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // Calculate the start of the week (Sunday)
  const weekStart = startOfWeek(startDate);
  
  // Generate array of days for the week
  const weekDays = Array.from({ length: 7 }).map((_, index) => addDays(weekStart, index));

  useEffect(() => {
    const fetchWeeklySchedule = async () => {
      setIsLoading(true);
      
      // Mock data for demonstration
      const mockSchedule: ScheduleItem[] = [
        {
          id: '1',
          className: 'English 101',
          timeStart: '09:00',
          timeEnd: '10:30',
          room: 'Room 101',
          teacher: 'John Smith',
          day: 1 // Monday
        },
        {
          id: '2',
          className: 'Math Advanced',
          timeStart: '11:00',
          timeEnd: '12:30',
          room: 'Room 102',
          teacher: 'Jane Doe',
          day: 1 // Monday
        },
        {
          id: '3',
          className: 'History 201',
          timeStart: '13:30',
          timeEnd: '15:00',
          room: 'Main Hall',
          teacher: 'Robert Johnson',
          day: 2 // Tuesday
        },
        {
          id: '4',
          className: 'Science 101',
          timeStart: '09:00',
          timeEnd: '10:30',
          room: 'Room 101',
          teacher: 'John Smith',
          day: 3 // Wednesday
        },
        {
          id: '5',
          className: 'Art Studio',
          timeStart: '14:00',
          timeEnd: '16:00',
          room: 'Art Room',
          teacher: 'Jane Doe',
          day: 4 // Thursday
        }
      ];
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 700));
      
      setWeeklySchedule(mockSchedule);
      setIsLoading(false);
    };
    
    fetchWeeklySchedule();
  }, [startDate]);

  const getScheduleForDay = (dayIndex: number) => {
    return weeklySchedule.filter(item => item.day === dayIndex);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">
          Weekly Schedule: {format(weekStart, 'MMM d')} - {format(addDays(weekStart, 6), 'MMM d, yyyy')}
        </h2>
        <Button variant="outline">
          Export
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          {Array.from({ length: 7 }).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      ) : (
        <div className="space-y-6">
          {weekDays.map((day, index) => (
            <Card key={index} className="overflow-hidden">
              <div className={`h-1 ${index === 0 || index === 6 ? 'bg-muted' : 'bg-primary'}`}></div>
              <CardContent className="p-4">
                <h3 className="font-medium text-lg mb-3">
                  {format(day, 'EEEE, MMMM d')}
                </h3>
                
                {getScheduleForDay(index).length === 0 ? (
                  <div className="text-center p-4 border border-dashed rounded-lg">
                    <p className="text-muted-foreground">No classes scheduled</p>
                  </div>
                ) : (
                  <div className="grid gap-2">
                    {getScheduleForDay(index).map((item, idx) => (
                      <div key={idx} className="p-3 border rounded-md grid grid-cols-12 gap-2">
                        <div className="col-span-2 md:col-span-1 font-medium text-sm text-muted-foreground">
                          {item.timeStart}
                        </div>
                        <div className="col-span-7 md:col-span-8">
                          <div className="font-medium">{item.className}</div>
                          <div className="text-sm text-muted-foreground">
                            {item.teacher} • {item.room}
                          </div>
                        </div>
                        <div className="col-span-3 text-right text-sm text-muted-foreground">
                          {item.timeStart} - {item.timeEnd}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default WeeklyScheduleView;
