// API client configuration and utilities
import { getAuthCookies } from './auth';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

// API client class
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    // Get auth token
    const token = getAuthCookies();
    
    // Default headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add auth header if token exists
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // HTTP methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// Error handling utility
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Auth API endpoints
export const authApi = {
  login: (credentials: { username: string; password: string }) =>
    apiClient.post<{ token: string; user: any }>('/auth/login', credentials),
  
  createUser: (userData: any) =>
    apiClient.post<ApiResponse<any>>('/auth/users', userData),
};

// Students API endpoints
export const studentsApi = {
  getStudents: (params?: any) =>
    apiClient.get<PaginatedResponse<any>>('/students', params),
  
  getStudent: (id: string) =>
    apiClient.get<ApiResponse<any>>(`/students/${id}`),
  
  createStudent: (data: any) =>
    apiClient.post<ApiResponse<any>>('/students', data),
  
  updateStudent: (id: string, data: any) =>
    apiClient.patch<ApiResponse<any>>(`/students/${id}`, data),
  
  transferStudent: (id: string, data: any) =>
    apiClient.post<ApiResponse<any>>(`/students/${id}/transfer`, data),
  
  recordPayment: (id: string, data: any) =>
    apiClient.post<ApiResponse<any>>(`/students/${id}/payments`, data),
  
  getPaymentHistory: (id: string, params?: any) =>
    apiClient.get<PaginatedResponse<any>>(`/students/${id}/payments`, params),
};

// Classes API endpoints
export const classesApi = {
  getClasses: (params?: any) =>
    apiClient.get<PaginatedResponse<any>>('/classes', params),
  
  getClass: (id: string) =>
    apiClient.get<ApiResponse<any>>(`/classes/${id}`),
  
  createClass: (data: any) =>
    apiClient.post<ApiResponse<any>>('/classes', data),
  
  updateClass: (id: string, data: any) =>
    apiClient.patch<ApiResponse<any>>(`/classes/${id}`, data),
};

// Attendance API endpoints
export const attendanceApi = {
  getAttendance: (params?: any) =>
    apiClient.get<PaginatedResponse<any>>('/attendance', params),
  
  markAttendance: (classId: string, data: any) =>
    apiClient.post<ApiResponse<any>>(`/attendance/classes/${classId}`, data),
  
  bulkMarkAttendance: (classId: string, data: any) =>
    apiClient.post<ApiResponse<any>>(`/attendance/classes/${classId}/bulk`, data),
};

// Payments API endpoints
export const paymentsApi = {
  getPayments: (params?: any) =>
    apiClient.get<PaginatedResponse<any>>('/payments', params),
  
  createPayment: (data: any) =>
    apiClient.post<ApiResponse<any>>('/payments', data),
  
  voidPayment: (id: string, data: any) =>
    apiClient.post<ApiResponse<any>>(`/payments/${id}/void`, data),
};

// Notes API endpoints
export const notesApi = {
  getNotes: (params?: any) =>
    apiClient.get<PaginatedResponse<any>>('/notes', params),
  
  createNote: (data: any) =>
    apiClient.post<ApiResponse<any>>('/notes', data),
  
  updateNote: (id: string, data: any) =>
    apiClient.patch<ApiResponse<any>>(`/notes/${id}`, data),
  
  deleteNote: (id: string) =>
    apiClient.delete<ApiResponse<any>>(`/notes/${id}`),
};

// Rooms API endpoints
export const roomsApi = {
  getRooms: (params?: any) =>
    apiClient.get<PaginatedResponse<any>>('/rooms', params),
  
  createRoom: (data: any) =>
    apiClient.post<ApiResponse<any>>('/rooms', data),
  
  updateRoom: (id: string, data: any) =>
    apiClient.patch<ApiResponse<any>>(`/rooms/${id}`, data),
};

// Users API endpoints
export const usersApi = {
  getUsers: (params?: any) =>
    apiClient.get<PaginatedResponse<any>>('/users', params),
  
  getUser: (id: string) =>
    apiClient.get<ApiResponse<any>>(`/users/${id}`),
  
  updateUserStatus: (id: string, data: any) =>
    apiClient.patch<ApiResponse<any>>(`/users/${id}/status`, data),
  
  updateUserRole: (id: string, data: any) =>
    apiClient.patch<ApiResponse<any>>(`/users/${id}/role`, data),
};
