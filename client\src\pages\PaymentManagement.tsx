
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchPayments } from "@/services/paymentService";
import { PaymentFilter } from "@/types/payment";
import MainLayout from "@/components/layout/MainLayout";
import PaymentTable from "@/components/payments/PaymentTable";
import PaymentFilters from "@/components/payments/PaymentFilters";
import PaymentStatisticsWidget from "@/components/payments/PaymentStatisticsWidget";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, FileDown, FileUp } from "lucide-react";

export default function PaymentManagement() {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<Partial<PaymentFilter>>({
    page: 1,
    limit: 10,
    sortBy: "date",
    sortOrder: "desc",
  });
  const [activeTab, setActiveTab] = useState("list");

  const { data, isLoading } = useQuery({
    queryKey: ["payments", filters],
    queryFn: () => fetchPayments(filters)
  });

  const payments = data?.data || [];
  const pagination = data?.pagination || {
    total: 0,
    page: 1,
    limit: 10,
    pages: 1,
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  const handleFilterChange = (newFilters: Partial<PaymentFilter>) => {
    setFilters(newFilters);
  };

  const handleResetFilters = () => {
    setFilters({
      page: 1,
      limit: 10,
      sortBy: "date",
      sortOrder: "desc",
    });
  };

  const handleNewPayment = () => {
    navigate("/payments/new");
  };

  const handleExport = () => {
    // This would be implemented to export payments in a real app
    console.log("Export payments with filters:", filters);
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between md:items-center space-y-2 md:space-y-0">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Payment Management</h1>
            <p className="text-muted-foreground">
              Manage and track all payment transactions
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleNewPayment}>
              <Plus className="mr-2 h-4 w-4" />
              New Payment
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <FileDown className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <Tabs 
          defaultValue="list"
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList>
            <TabsTrigger value="list">Payment List</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="list" className="space-y-4">
            <PaymentFilters
              filters={filters}
              onFilterChange={handleFilterChange}
              onResetFilters={handleResetFilters}
            />
            
            <PaymentTable
              payments={payments}
              isLoading={isLoading}
              pagination={pagination}
              onPageChange={handlePageChange}
            />
          </TabsContent>
          
          <TabsContent value="statistics">
            <PaymentStatisticsWidget />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
