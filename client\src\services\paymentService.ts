
import { 
  Payment, 
  PaymentFilter, 
  PaymentApiResponse, 
  PaymentDetailsApiResponse,
  PaymentStatistics,
  PaymentStatisticsApiResponse
} from "@/types/payment";

/**
 * Fetch payments with filtering options
 */
export const fetchPayments = async (filters: Partial<PaymentFilter> = {}): Promise<PaymentApiResponse> => {
  // In a real app, this would be an API call with query parameters
  // For this example, we'll return mock data
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock data - in real implementation, this would apply filters
  const mockPayments: Payment[] = [
    {
      id: "payment1",
      studentId: "student1",
      student: {
        id: "student1",
        firstName: "John",
        lastName: "Doe"
      },
      amount: 250.00,
      remainingBalance: 0,
      status: "completed",
      method: "cash",
      date: "2023-10-05",
      nextDueDate: "2023-11-05",
      description: "Monthly Tuition - October 2023",
      recordedBy: "admin",
      recordedAt: "2023-10-05T10:30:00Z",
      receiptNumber: "RCP-2023-001"
    },
    {
      id: "payment2",
      studentId: "student2",
      student: {
        id: "student2",
        firstName: "Alice",
        lastName: "Smith"
      },
      amount: 300.00,
      remainingBalance: 0,
      status: "completed",
      method: "card",
      date: "2023-10-06",
      nextDueDate: "2023-11-06",
      description: "Monthly Tuition - October 2023",
      period: "monthly",
      recordedBy: "admin",
      recordedAt: "2023-10-06T09:15:00Z",
      receiptNumber: "RCP-2023-002"
    },
    {
      id: "payment3",
      studentId: "student3",
      student: {
        id: "student3",
        firstName: "Michael",
        lastName: "Johnson"
      },
      amount: 500.00,
      remainingBalance: 0,
      status: "voided",
      method: "transfer",
      date: "2023-10-07",
      description: "Quarterly Tuition - Q4 2023",
      period: "quarterly",
      recordedBy: "admin",
      recordedAt: "2023-10-07T14:20:00Z",
      voidedBy: "admin",
      voidedAt: "2023-10-07T16:45:00Z",
      voidReason: "Incorrect amount",
      receiptNumber: "RCP-2023-003"
    },
    {
      id: "payment4",
      studentId: "student1",
      student: {
        id: "student1",
        firstName: "John",
        lastName: "Doe"
      },
      amount: 150.00,
      remainingBalance: 0,
      status: "completed",
      method: "cash",
      date: "2023-09-05",
      nextDueDate: "2023-10-05",
      description: "Monthly Tuition - September 2023",
      period: "monthly",
      recordedBy: "admin",
      recordedAt: "2023-09-05T11:10:00Z",
      receiptNumber: "RCP-2023-004"
    },
    {
      id: "payment5",
      studentId: "student4",
      student: {
        id: "student4",
        firstName: "Emily",
        lastName: "Davis"
      },
      amount: 250.00,
      remainingBalance: 250.00,
      status: "pending",
      method: "check",
      date: "2023-10-08",
      nextDueDate: "2023-11-08",
      description: "Monthly Tuition - October 2023",
      period: "monthly",
      recordedBy: "admin",
      recordedAt: "2023-10-08T10:00:00Z",
      notes: "Check pending clearance"
    }
  ];
  
  // Filter payments based on criteria
  let filteredPayments = [...mockPayments];
  
  // Apply student filter
  if (filters.studentId) {
    filteredPayments = filteredPayments.filter(p => p.studentId === filters.studentId);
  }
  
  // Apply status filter
  if (filters.status) {
    filteredPayments = filteredPayments.filter(p => p.status === filters.status);
  }
  
  // Apply method filter
  if (filters.method) {
    filteredPayments = filteredPayments.filter(p => p.method === filters.method);
  }
  
  // Apply date range filter
  if (filters.startDate) {
    const startDate = new Date(filters.startDate);
    filteredPayments = filteredPayments.filter(p => new Date(p.date) >= startDate);
  }
  
  if (filters.endDate) {
    const endDate = new Date(filters.endDate);
    filteredPayments = filteredPayments.filter(p => new Date(p.date) <= endDate);
  }
  
  // Apply amount range filter
  if (filters.minAmount !== undefined) {
    filteredPayments = filteredPayments.filter(p => p.amount >= filters.minAmount!);
  }
  
  if (filters.maxAmount !== undefined) {
    filteredPayments = filteredPayments.filter(p => p.amount <= filters.maxAmount!);
  }
  
  // Apply search filter
  if (filters.search) {
    const search = filters.search.toLowerCase();
    filteredPayments = filteredPayments.filter(p => 
      p.receiptNumber?.toLowerCase().includes(search) ||
      p.description.toLowerCase().includes(search) ||
      p.student?.firstName.toLowerCase().includes(search) ||
      p.student?.lastName.toLowerCase().includes(search)
    );
  }
  
  // Sort payments
  if (filters.sortBy) {
    filteredPayments.sort((a, b) => {
      let valueA, valueB;
      
      switch (filters.sortBy) {
        case 'date':
          valueA = new Date(a.date);
          valueB = new Date(b.date);
          break;
        case 'amount':
          valueA = a.amount;
          valueB = b.amount;
          break;
        case 'studentName':
          valueA = `${a.student?.lastName} ${a.student?.firstName}`;
          valueB = `${b.student?.lastName} ${b.student?.firstName}`;
          break;
        case 'status':
          valueA = a.status;
          valueB = b.status;
          break;
        case 'method':
          valueA = a.method;
          valueB = b.method;
          break;
        case 'recordedAt':
          valueA = new Date(a.recordedAt);
          valueB = new Date(b.recordedAt);
          break;
        default:
          valueA = new Date(a.date);
          valueB = new Date(b.date);
      }
      
      const order = filters.sortOrder === 'asc' ? 1 : -1;
      
      if (valueA < valueB) return -1 * order;
      if (valueA > valueB) return 1 * order;
      return 0;
    });
  } else {
    // Default sort by date descending
    filteredPayments.sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  }
  
  // Apply pagination
  const page = filters.page || 1;
  const limit = filters.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedPayments = filteredPayments.slice(startIndex, endIndex);
  
  return {
    success: true,
    data: paginatedPayments,
    pagination: {
      total: filteredPayments.length,
      page: page,
      limit: limit,
      pages: Math.ceil(filteredPayments.length / limit)
    }
  };
};

/**
 * Fetch a payment by ID
 */
export const fetchPayment = async (paymentId: string): Promise<PaymentDetailsApiResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock payment data
  const payment: Payment = {
    id: paymentId,
    studentId: "student1",
    student: {
      id: "student1",
      firstName: "John",
      lastName: "Doe"
    },
    amount: 250.00,
    remainingBalance: 0,
    status: "completed",
    method: "cash",
    date: "2023-10-05",
    nextDueDate: "2023-11-05",
    description: "Monthly Tuition - October 2023",
    period: "monthly",
    recordedBy: "admin",
    recordedAt: "2023-10-05T10:30:00Z",
    receiptNumber: "RCP-2023-001",
    notes: "Payment received on time",
    attachments: [
      {
        id: "attach1",
        fileName: "receipt.pdf",
        fileUrl: "/receipts/receipt-001.pdf",
        uploadedAt: "2023-10-05T10:35:00Z",
        uploadedBy: "admin"
      }
    ]
  };
  
  return {
    success: true,
    data: payment
  };
};

/**
 * Record a new payment
 */
export const createPayment = async (paymentData: Partial<Payment>): Promise<PaymentDetailsApiResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Generate a receipt number
  const receiptNumber = `RCP-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`;
  
  // Create a new payment
  const newPayment: Payment = {
    id: `payment${Date.now()}`,
    studentId: paymentData.studentId || "",
    amount: paymentData.amount || 0,
    status: "completed",
    method: paymentData.method || "cash",
    date: paymentData.date || new Date().toISOString().split('T')[0],
    nextDueDate: paymentData.nextDueDate,
    description: paymentData.description || "",
    period: paymentData.period,
    recordedBy: "current-user",
    recordedAt: new Date().toISOString(),
    receiptNumber: receiptNumber,
    notes: paymentData.notes,
    attachments: paymentData.attachments
  };
  
  return {
    success: true,
    data: newPayment
  };
};

/**
 * Update a payment
 */
export const updatePayment = async (paymentId: string, paymentData: Partial<Payment>): Promise<PaymentDetailsApiResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Get the existing payment
  const { data: existingPayment } = await fetchPayment(paymentId);
  
  // Update the payment
  const updatedPayment: Payment = {
    ...existingPayment,
    ...paymentData,
    modifiedBy: "current-user",
    modifiedAt: new Date().toISOString()
  };
  
  return {
    success: true,
    data: updatedPayment
  };
};

/**
 * Void a payment
 */
export const voidPayment = async (paymentId: string, reason: string): Promise<PaymentDetailsApiResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Get the existing payment
  const { data: existingPayment } = await fetchPayment(paymentId);
  
  // Void the payment
  const voidedPayment: Payment = {
    ...existingPayment,
    status: "voided",
    voidedBy: "current-user",
    voidedAt: new Date().toISOString(),
    voidReason: reason
  };
  
  return {
    success: true,
    data: voidedPayment
  };
};

/**
 * Get payment statistics
 */
export const getPaymentStatistics = async (
  startDate?: string,
  endDate?: string,
  groupBy: 'daily' | 'monthly' | 'method' | 'status' = 'monthly',
  includeVoided: boolean = false
): Promise<PaymentStatisticsApiResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock statistics data
  const mockStatistics: PaymentStatistics = {
    totalAmount: 1450.00,
    totalCount: 5,
    periodBreakdown: [
      { label: "Sep 2023", value: 400, count: 2 },
      { label: "Oct 2023", value: 1050, count: 3 }
    ],
    methodBreakdown: [
      { method: "cash", value: 650, count: 2, percentage: 44.83 },
      { method: "card", value: 300, count: 1, percentage: 20.69 },
      { method: "transfer", value: 500, count: 1, percentage: 34.48 },
      { method: "check", value: 0, count: 0, percentage: 0 },
      { method: "other", value: 0, count: 0, percentage: 0 }
    ],
    statusBreakdown: [
      { status: "completed", value: 950, count: 3, percentage: 65.52 },
      { status: "pending", value: 0, count: 0, percentage: 0 },
      { status: "voided", value: 500, count: 1, percentage: 34.48 }
    ],
    overdueCount: 1,
    overdueAmount: 250,
    upcomingCount: 2
  };
  
  return {
    success: true,
    data: mockStatistics
  };
};

/**
 * Generate a receipt for a payment
 */
export const generateReceipt = async (
  paymentId: string, 
  format: 'pdf' | 'html' = 'pdf'
): Promise<{success: boolean, url: string}> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, this would generate a receipt and return the URL
  return {
    success: true,
    url: `/receipts/${paymentId}.${format}`
  };
};

/**
 * Get payment history for a student
 */
export const getStudentPaymentHistory = async (
  studentId: string
): Promise<PaymentApiResponse> => {
  // Use the existing fetchPayments function with a student filter
  return fetchPayments({ studentId, page: 1, limit: 100 });
};

/**
 * Export payments
 */
export const exportPayments = async (
  filters: Partial<PaymentFilter> = {},
  format: 'csv' | 'excel' = 'csv'
): Promise<{success: boolean, url: string}> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, this would generate an export file and return the URL
  return {
    success: true,
    url: `/exports/payments.${format}`
  };
};
