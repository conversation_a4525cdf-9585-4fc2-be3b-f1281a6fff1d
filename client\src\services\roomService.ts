
import { 
  Room, 
  RoomStatus, 
  MaintenancePeriod, 
  RoomScheduleItem, 
  RoomUtilization,
  RoomFilters,
  PaginatedRoomsResponse,
  RoomAvailabilityResponse,
  AvailableRoomsResponse
} from "@/types/room";
import { toast } from "sonner";
import { capitalizeFirstLetter } from "@/lib/utils";

// Mock buildings and floors for filtering
export const BUILDINGS = ["Main Campus", "North Wing", "South Wing", "East Building", "West Building"];
export const FLOORS = [0, 1, 2, 3, 4];
export const ROOM_FEATURES = [
  { value: "projector", label: "Projector" },
  { value: "whiteboard", label: "Whiteboard" },
  { value: "smartboard", label: "Smart Board" },
  { value: "computers", label: "Computers" },
  { value: "wifi", label: "WiFi" },
  { value: "audioSystem", label: "Audio System" },
  { value: "videoConference", label: "Video Conference" },
  { value: "airConditioning", label: "Air Conditioning" },
  { value: "laboratoryEquipment", label: "Laboratory Equipment" },
  { value: "specializedSoftware", label: "Specialized Software" },
  { value: "accessibleFacilities", label: "Accessible Facilities" }
];

// Function to fetch rooms with filters
export const fetchRooms = async (filters: RoomFilters = {}): Promise<PaginatedRoomsResponse> => {
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Mock data for rooms
    const mockRooms: Room[] = Array.from({ length: 50 }, (_, i) => {
      const id = `room-${i + 1}`;
      const building = BUILDINGS[Math.floor(Math.random() * BUILDINGS.length)];
      const floor = FLOORS[Math.floor(Math.random() * FLOORS.length)];
      const capacity = Math.floor(Math.random() * 40) + 10; // 10-50 capacity
      
      // Generate random features
      const allFeatures = ROOM_FEATURES.map(f => f.value) as any[];
      const featuresCount = Math.floor(Math.random() * 6) + 1; // 1-6 features
      const features = Array.from({ length: featuresCount }, () => {
        const randomIndex = Math.floor(Math.random() * allFeatures.length);
        return allFeatures[randomIndex];
      });
      
      // Random status with weighted distribution
      const statusRandom = Math.random();
      let status: RoomStatus = 'active';
      if (statusRandom > 0.85) status = 'maintenance';
      else if (statusRandom > 0.7) status = 'inactive';
      
      return {
        id,
        name: `${building.substring(0, 1)}${floor}${String(i + 1).padStart(2, '0')}`,
        capacity,
        building,
        floor,
        features,
        status,
        // Random current schedule for some rooms
        currentSchedule: Math.random() > 0.7 ? {
          classId: `class-${Math.floor(Math.random() * 100)}`,
          className: `${['English', 'Math', 'Science', 'History'][Math.floor(Math.random() * 4)]} ${['Basic', 'Intermediate', 'Advanced'][Math.floor(Math.random() * 3)]}`,
          teacherName: `${['John', 'Jane', 'Michael', 'Sarah', 'Robert'][Math.floor(Math.random() * 5)]} ${['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'][Math.floor(Math.random() * 5)]}`,
          startTime: new Date(new Date().setHours(Math.floor(Math.random() * 8) + 9, 0, 0)).toISOString(),
          endTime: new Date(new Date().setHours(Math.floor(Math.random() * 8) + 10, 30, 0)).toISOString(),
        } : undefined,
        lastModified: {
          timestamp: new Date(new Date().setDate(new Date().getDate() - Math.floor(Math.random() * 30))).toISOString(),
          userId: `user-${Math.floor(Math.random() * 10) + 1}`,
          userName: `${['Admin', 'Manager', 'Secretary'][Math.floor(Math.random() * 3)]} User`,
        }
      };
    });
    
    // Apply filters
    let filteredRooms = [...mockRooms];
    
    // Filter by search term
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredRooms = filteredRooms.filter(room => 
        room.name.toLowerCase().includes(searchLower) || 
        room.building.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by status
    if (filters.status) {
      filteredRooms = filteredRooms.filter(room => room.status === filters.status);
    }
    
    // Filter by building
    if (filters.building) {
      filteredRooms = filteredRooms.filter(room => room.building === filters.building);
    }
    
    // Filter by floor
    if (filters.floor !== undefined) {
      filteredRooms = filteredRooms.filter(room => room.floor === filters.floor);
    }
    
    // Filter by minimum capacity
    if (filters.minCapacity) {
      filteredRooms = filteredRooms.filter(room => room.capacity >= filters.minCapacity);
    }
    
    // Filter by features
    if (filters.features && filters.features.length > 0) {
      filteredRooms = filteredRooms.filter(room => 
        filters.features!.every(feature => room.features.includes(feature))
      );
    }
    
    // Apply sorting
    if (filters.sortBy) {
      filteredRooms.sort((a, b) => {
        let aValue: any = a[filters.sortBy as keyof Room];
        let bValue: any = b[filters.sortBy as keyof Room];
        
        // Handle nested properties
        if (filters.sortBy === 'building') {
          aValue = a.building;
          bValue = b.building;
        } else if (filters.sortBy === 'floor') {
          aValue = a.floor;
          bValue = b.floor;
        }
        
        // Compare values
        if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
        return 0;
      });
    }
    
    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedRooms = filteredRooms.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: paginatedRooms,
      pagination: {
        total: filteredRooms.length,
        page,
        limit,
        pages: Math.ceil(filteredRooms.length / limit)
      }
    };
  } catch (error) {
    console.error("Error fetching rooms:", error);
    toast.error("Failed to fetch rooms");
    throw error;
  }
};

// Function to fetch a specific room
export const fetchRoom = async (roomId: string): Promise<Room> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate a consistent mock room for the given ID
    const idNumber = parseInt(roomId.replace(/\D/g, '') || '0');
    const buildingIndex = idNumber % BUILDINGS.length;
    const floorIndex = idNumber % FLOORS.length;
    
    const room: Room = {
      id: roomId,
      name: `${BUILDINGS[buildingIndex].substring(0, 1)}${FLOORS[floorIndex]}${String(idNumber).padStart(2, '0')}`,
      capacity: (idNumber % 20) + 20, // 20-40 capacity
      building: BUILDINGS[buildingIndex],
      floor: FLOORS[floorIndex],
      features: ROOM_FEATURES.slice(0, (idNumber % 8) + 1).map(f => f.value) as any[],
      status: (['active', 'maintenance', 'inactive'] as RoomStatus[])[idNumber % 3],
      maintenanceSchedule: idNumber % 3 === 1 ? [
        {
          id: `maint-${roomId}-1`,
          startDate: new Date(new Date().setDate(new Date().getDate() + 2)).toISOString(),
          endDate: new Date(new Date().setDate(new Date().getDate() + 4)).toISOString(),
          reason: "Routine maintenance and cleaning",
          scheduledBy: {
            id: "user-1",
            name: "Admin User"
          },
          createdAt: new Date(new Date().setDate(new Date().getDate() - 5)).toISOString()
        }
      ] : [],
      lastModified: {
        timestamp: new Date(new Date().setDate(new Date().getDate() - (idNumber % 10))).toISOString(),
        userId: `user-${(idNumber % 5) + 1}`,
        userName: `${['Admin', 'Manager', 'Secretary'][(idNumber % 3)]} User`,
      }
    };
    
    return room;
  } catch (error) {
    console.error(`Error fetching room ${roomId}:`, error);
    toast.error("Failed to fetch room details");
    throw error;
  }
};

// Function to create a new room
export const createRoom = async (roomData: Partial<Room>): Promise<Room> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Validate required fields
    if (!roomData.name || !roomData.capacity || !roomData.building || roomData.floor === undefined) {
      throw new Error("Missing required room information");
    }
    
    // Generate a new room ID
    const newRoom: Room = {
      id: `room-${Date.now()}`,
      name: roomData.name,
      capacity: roomData.capacity,
      building: roomData.building,
      floor: roomData.floor,
      features: roomData.features || [],
      status: roomData.status || 'active',
      lastModified: {
        timestamp: new Date().toISOString(),
        userId: "current-user-id", // Would be replaced with actual current user ID
        userName: "Current User", // Would be replaced with actual current user name
      }
    };
    
    toast.success(`Room ${newRoom.name} created successfully`);
    return newRoom;
  } catch (error) {
    console.error("Error creating room:", error);
    toast.error("Failed to create room");
    throw error;
  }
};

// Function to update a room
export const updateRoom = async (roomId: string, roomData: Partial<Room>): Promise<Room> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Fetch existing room first (in a real implementation)
    const existingRoom = await fetchRoom(roomId);
    
    // Update room data
    const updatedRoom: Room = {
      ...existingRoom,
      ...roomData,
      lastModified: {
        timestamp: new Date().toISOString(),
        userId: "current-user-id", // Would be replaced with actual current user ID
        userName: "Current User", // Would be replaced with actual current user name
      }
    };
    
    toast.success(`Room ${updatedRoom.name} updated successfully`);
    return updatedRoom;
  } catch (error) {
    console.error(`Error updating room ${roomId}:`, error);
    toast.error("Failed to update room");
    throw error;
  }
};

// Function to check room availability
export const checkRoomAvailability = async (
  roomId: string, 
  date: string, 
  timeStart: string, 
  timeEnd: string
): Promise<RoomAvailabilityResponse> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Randomly determine if the room is available (70% chance of availability)
    const isAvailable = Math.random() > 0.3;
    
    if (isAvailable) {
      return { available: true };
    } else {
      // Generate conflicting events
      return {
        available: false,
        conflictingEvents: [
          {
            id: `event-${Date.now()}`,
            type: Math.random() > 0.5 ? 'class' : 'maintenance',
            name: Math.random() > 0.5 ? 'Advanced English' : 'Scheduled Maintenance',
            start: `${date}T${timeStart}`,
            end: `${date}T${timeEnd}`
          }
        ]
      };
    }
  } catch (error) {
    console.error(`Error checking availability for room ${roomId}:`, error);
    toast.error("Failed to check room availability");
    throw error;
  }
};

// Function to find available rooms for a time slot
export const findAvailableRooms = async (
  date: string,
  timeStart: string,
  timeEnd: string,
  filters: RoomFilters = {}
): Promise<AvailableRoomsResponse> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Get all rooms first (reuse the existing function)
    const allRoomsResponse = await fetchRooms(filters);
    
    // Filter to only include available rooms (in a real implementation, this would check against schedules)
    // For mock, we'll randomly mark rooms as available (80% chance)
    const availableRooms = allRoomsResponse.data.filter(room => 
      room.status === 'active' && Math.random() > 0.2
    );
    
    return {
      success: true,
      data: availableRooms,
      pagination: {
        ...allRoomsResponse.pagination,
        total: availableRooms.length,
        pages: Math.ceil(availableRooms.length / (filters.limit || 10))
      }
    };
  } catch (error) {
    console.error("Error finding available rooms:", error);
    toast.error("Failed to find available rooms");
    throw error;
  }
};

// Function to schedule room maintenance
export const scheduleRoomMaintenance = async (
  roomId: string,
  startDate: string,
  endDate: string,
  reason: string
): Promise<MaintenancePeriod> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if dates are valid
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime()) || start >= end) {
      throw new Error("Invalid date range provided");
    }
    
    // Create maintenance record
    const maintenancePeriod: MaintenancePeriod = {
      id: `maint-${Date.now()}`,
      startDate,
      endDate,
      reason,
      scheduledBy: {
        id: "current-user-id", // Would be replaced with actual current user ID
        name: "Current User", // Would be replaced with actual current user name
      },
      createdAt: new Date().toISOString()
    };
    
    toast.success("Maintenance scheduled successfully");
    return maintenancePeriod;
  } catch (error) {
    console.error(`Error scheduling maintenance for room ${roomId}:`, error);
    toast.error("Failed to schedule maintenance");
    throw error;
  }
};

// Function to fetch room schedule
export const fetchRoomSchedule = async (
  roomId: string,
  startDate: string,
  endDate: string
): Promise<RoomScheduleItem[]> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 700));
    
    // Generate mock schedule items
    const start = new Date(startDate);
    const end = new Date(endDate);
    const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    const scheduleItems: RoomScheduleItem[] = [];
    
    // Generate classes and maintenance periods
    for (let i = 0; i < daysDiff; i++) {
      const currentDate = new Date(start);
      currentDate.setDate(currentDate.getDate() + i);
      
      // Skip weekends (0 = Sunday, 6 = Saturday)
      if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
        continue;
      }
      
      // Add 2-4 classes per day
      const classCount = Math.floor(Math.random() * 3) + 2;
      const classStartHours = [9, 11, 14, 16, 18];
      
      for (let j = 0; j < classCount; j++) {
        if (j < classStartHours.length) {
          const classStartHour = classStartHours[j];
          const classStartTime = new Date(currentDate);
          classStartTime.setHours(classStartHour, 0, 0, 0);
          
          const classEndTime = new Date(classStartTime);
          classEndTime.setHours(classStartHour + 1, 30, 0, 0);
          
          // Randomly determine class colors based on subject
          const subjects = [
            { name: "English", color: "#4CAF50" },
            { name: "Math", color: "#2196F3" },
            { name: "Science", color: "#FF9800" },
            { name: "History", color: "#9C27B0" },
            { name: "Languages", color: "#E91E63" }
          ];
          
          const subject = subjects[Math.floor(Math.random() * subjects.length)];
          const level = ["Basic", "Intermediate", "Advanced"][Math.floor(Math.random() * 3)];
          
          scheduleItems.push({
            id: `class-${i}-${j}`,
            title: `${subject.name} ${level}`,
            type: 'class',
            start: classStartTime.toISOString(),
            end: classEndTime.toISOString(),
            classId: `class-${Math.floor(Math.random() * 100)}`,
            teacherId: `teacher-${Math.floor(Math.random() * 20)}`,
            teacherName: `${['John', 'Jane', 'Michael', 'Sarah', 'Robert'][Math.floor(Math.random() * 5)]} ${['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'][Math.floor(Math.random() * 5)]}`,
            backgroundColor: subject.color
          });
        }
      }
    }
    
    // Add maintenance period(s) if relevant
    if (Math.random() > 0.7) {
      const maintenanceStart = new Date(start);
      maintenanceStart.setDate(maintenanceStart.getDate() + Math.floor(Math.random() * (daysDiff - 1)));
      maintenanceStart.setHours(7, 0, 0, 0); // Early morning
      
      const maintenanceEnd = new Date(maintenanceStart);
      maintenanceEnd.setHours(8, 30, 0, 0);
      
      scheduleItems.push({
        id: `maint-${Date.now()}`,
        title: "Room Maintenance",
        type: 'maintenance',
        start: maintenanceStart.toISOString(),
        end: maintenanceEnd.toISOString(),
        backgroundColor: "#F44336" // Red for maintenance
      });
    }
    
    return scheduleItems.sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());
  } catch (error) {
    console.error(`Error fetching schedule for room ${roomId}:`, error);
    toast.error("Failed to fetch room schedule");
    throw error;
  }
};

// Function to get room utilization statistics
export const fetchRoomUtilization = async (
  roomId: string,
  startDate: string,
  endDate: string
): Promise<RoomUtilization> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 900));
    
    // Generate random utilization data
    const usagePercentage = Math.floor(Math.random() * 50) + 30; // 30-80% usage
    const totalHours = (Math.floor(Math.random() * 5) + 6) * 5; // 30-50 hours per week
    const usedHours = Math.floor(totalHours * (usagePercentage / 100));
    
    // Generate peak times
    const peakTimes = [
      {
        day: "Monday",
        time: "10:00-12:00",
        usagePercentage: Math.floor(Math.random() * 30) + 70 // 70-100%
      },
      {
        day: "Wednesday",
        time: "14:00-16:00",
        usagePercentage: Math.floor(Math.random() * 30) + 70 // 70-100%
      },
      {
        day: "Thursday",
        time: "16:00-18:00",
        usagePercentage: Math.floor(Math.random() * 30) + 70 // 70-100%
      }
    ];
    
    return {
      period: `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`,
      usagePercentage,
      totalHours,
      usedHours,
      availableHours: totalHours - usedHours,
      peakTimes
    };
  } catch (error) {
    console.error(`Error fetching utilization for room ${roomId}:`, error);
    toast.error("Failed to fetch room utilization data");
    throw error;
  }
};
