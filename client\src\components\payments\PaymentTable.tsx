
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Payment } from "@/types/payment";
import { Eye, FileText, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import Pagination from "@/components/common/Pagination";

interface PaymentTableProps {
  payments: Payment[];
  isLoading: boolean;
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
  onPageChange: (page: number) => void;
}

const PaymentTable = ({ payments, isLoading, pagination, onPageChange }: PaymentTableProps) => {
  const navigate = useNavigate();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const handleViewPayment = (paymentId: string) => {
    navigate(`/payments/${paymentId}`);
  };

  const handleGenerateReceipt = (paymentId: string) => {
    // Changed from /receipts/${paymentId} to /payments/${paymentId} with receipt tab
    navigate(`/payments/${paymentId}?tab=receipt`);
  };

  if (isLoading) {
    return (
      <div className="h-96 flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Receipt #</TableHead>
                <TableHead>Student</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.length > 0 ? (
                payments.map((payment) => (
                  <TableRow key={payment.id} className="group">
                    <TableCell className="font-medium">
                      {payment.receiptNumber || "—"}
                    </TableCell>
                    <TableCell>
                      {payment.student ? (
                        <span className="font-medium">
                          {payment.student.lastName}, {payment.student.firstName}
                        </span>
                      ) : (
                        "Unknown Student"
                      )}
                    </TableCell>
                    <TableCell className="max-w-[200px] truncate">
                      {payment.description}
                    </TableCell>
                    <TableCell>{formatDate(payment.date)}</TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(payment.amount)}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className="capitalize"
                      >
                        {payment.method.replace("_", " ")}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={cn(
                          "capitalize",
                          payment.status === "completed" &&
                            "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 dark:border-green-900",
                          payment.status === "pending" &&
                            "border-yellow-200 bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-900",
                          payment.status === "voided" &&
                            "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 dark:border-red-900"
                        )}
                      >
                        {payment.status}
                        {payment.status === "voided" && (
                          <AlertCircle className="ml-1 h-3 w-3" />
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewPayment(payment.id)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      {payment.status !== "voided" && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleGenerateReceipt(payment.id)}
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          Receipt
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="h-32 text-center text-muted-foreground"
                  >
                    No payments found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      
      {!isLoading && pagination.pages > 1 && (
        <div className="flex justify-end">
          <Pagination
            page={pagination.page}
            totalPages={pagination.pages} // Changed from count to totalPages
            onPageChange={onPageChange}
          />
        </div>
      )}
    </div>
  );
};

export default PaymentTable;
