
// Basic room types
export interface Room {
  id: string;
  name: string;
  type: string;
  capacity: number;
  building: string;
  floor: string;
  amenities: string[];
  features?: string[];
  status: 'available' | 'occupied' | 'maintenance' | 'active' | 'inactive';
  schedule?: RoomSchedule[];
  notes?: string;
  lastMaintenance?: string;
  nextMaintenance?: string;
  maintenanceSchedule?: MaintenancePeriod[];
  lastModified?: {
    timestamp: string;
    userId: string;
    userName: string;
  };
  currentSchedule?: {
    classId: string;
    className: string;
    teacherName: string;
    startTime: string;
    endTime: string;
  };
}

export interface RoomSchedule {
  id: string;
  day: string;
  timeStart: string;
  timeEnd: string;
  classId?: string;
  className?: string;
  teacherId?: string;
  teacherName?: string;
  recurring: boolean;
}

export interface RoomAvailability {
  id: string;
  name: string;
  available: boolean;
  conflicts?: string[];
}

// Enhanced Room filters
export interface RoomFilter {
  type?: string;
  capacity?: number;
  amenities?: string[];
  building?: string;
  floor?: string;
  status?: string;
  features?: string[];
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  minCapacity?: number;
}

// Alias for backward compatibility
export type RoomFilters = RoomFilter;

// Room schedule item (used for calendar display)
export interface RoomScheduleItem {
  id: string;
  title: string;
  type: 'class' | 'maintenance' | 'event';
  start: string;
  end: string;
  classId?: string;
  teacherId?: string;
  teacherName?: string;
  backgroundColor?: string;
}

// Room utilization stats
export interface RoomUtilization {
  period: string;
  usagePercentage: number;
  totalHours: number;
  usedHours: number;
  availableHours: number;
  peakTimes: {
    day: string;
    time: string;
    usagePercentage: number;
  }[];
}

// Maintenance periods
export interface MaintenancePeriod {
  id: string;
  startDate: string;
  endDate: string;
  reason: string;
  scheduledBy: {
    id: string;
    name: string;
  };
  createdAt: string;
}

// API Response types
export interface PaginatedRoomsResponse {
  success: boolean;
  data: Room[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface RoomAvailabilityResponse {
  available: boolean;
  conflictingEvents?: {
    id: string;
    type: string;
    name: string;
    start: string;
    end: string;
  }[];
}

export interface AvailableRoomsResponse {
  success: boolean;
  data: Room[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// Utility type for room features
export type RoomFeature = string;
export type RoomStatus = 'available' | 'occupied' | 'maintenance' | 'active' | 'inactive';
