
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { isAuthenticated, getCurrentUser } from "@/lib/auth";
import MainLayout from "@/components/layout/MainLayout";
import WelcomeBar from "@/components/dashboard/WelcomeBar";
import DateRangeSelector from "@/components/dashboard/DateRangeSelector";
import RoleBasedDashboard from "@/components/dashboard/RoleBasedDashboard";
import { UserRole } from "@/types";

const Dashboard = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [dateRange, setDateRange] = useState({ 
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 
    to: new Date() 
  });
  const [userRole, setUserRole] = useState<UserRole>('Guest');
  
  // Check authentication and get user role
  useEffect(() => {
    const checkAuth = async () => {
      const isAuth = await isAuthenticated();
      if (!isAuth) {
        navigate("/");
        return;
      }
      
      const user = await getCurrentUser();
      if (user) {
        setUserRole(user.role || 'Guest');
      }
    };
    
    checkAuth();
  }, [navigate]);

  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4">
        {/* Welcome message and stats summary */}
        <WelcomeBar stats={{ 
          totalStudents: 583, 
          activeClasses: 32 
        }} />
        
        {/* Date range selector */}
        <div className="mb-6 flex justify-end">
          <DateRangeSelector onRangeChange={setDateRange} />
        </div>
        
        {/* Role-based dashboard content */}
        <RoleBasedDashboard 
          userRole={userRole}
          dateRange={dateRange}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
        />
      </div>
    </MainLayout>
  );
};

export default Dashboard;
