// server/src/config/cors.config.ts
export const CORS_CONFIG = {
    // Allow specific IP ranges for local network
    ALLOWED_ORIGINS: [
      'http://localhost:3000',     // Development
      'http://localhost:5173',
      /^http:\/\/192\.168\.\d+\.\d+$/,  // Local network 192.168.x.x
      /^http:\/\/10\.\d+\.\d+\.\d+$/,   // Local network 10.x.x.x
      /^http:\/\/172\.(1[6-9]|2\d|3[0-1])\.\d+\.\d+$/ // Local network 172.16-31.x.x
    ],
    ALLOWED_METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    ALLOWED_HEADERS: ['Content-Type', 'Authorization'],
    EXPOSE_HEADERS: ['Content-Range', 'X-Total-Count'],
    MAX_AGE: 600, // 10 minutes
    CREDENTIALS: true
  };