
import React from "react";
import MainLayout from "@/components/layout/MainLayout";
import ScheduleCalendar from "@/components/schedule/ScheduleCalendar";
import ScheduleFilters from "@/components/schedule/ScheduleFilters";
import { hasRole } from "@/lib/auth";
import { Navigate } from "react-router-dom";

const SchedulePage = () => {
  // Check if user has proper permissions
  const canViewSchedule = hasRole(['SuperAdmin', 'Manager', 'Secretary', 'Teacher']);

  if (!canViewSchedule) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Class Schedule</h1>
        </div>
        <ScheduleCalendar />
      </div>
    </MainLayout>
  );
};

export default SchedulePage;
